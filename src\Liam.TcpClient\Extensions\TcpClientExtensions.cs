using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Liam.TcpClient.Interfaces;
using Liam.TcpClient.Models;
using Liam.TcpClient.Security;
using Liam.TcpClient.Performance;

namespace Liam.TcpClient.Extensions;

/// <summary>
/// TCP客户端扩展方法
/// </summary>
public static class TcpClientExtensions
{
    /// <summary>
    /// 发送JSON对象（高性能版本）
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="client">TCP客户端</param>
    /// <param name="obj">要发送的对象</param>
    /// <param name="options">JSON序列化选项</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public static async Task<bool> SendJsonAsync<T>(this ITcpClient client, T obj, JsonSerializerOptions? options = null, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(client);
        ArgumentNullException.ThrowIfNull(obj);

        // 直接序列化为字节数组，避免中间字符串创建
        var jsonBytes = StringOptimizer.SerializeToBytes(obj, options);
        return await client.SendAsync(jsonBytes, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 发送JSON对象到流（零分配版本）
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="client">TCP客户端</param>
    /// <param name="obj">要发送的对象</param>
    /// <param name="options">JSON序列化选项</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public static async Task<bool> SendJsonToStreamAsync<T>(this ITcpClient client, T obj, JsonSerializerOptions? options = null, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(client);
        ArgumentNullException.ThrowIfNull(obj);

        using var memoryStream = new MemoryStream();
        await StringOptimizer.SerializeToStreamAsync(obj, memoryStream, options, cancellationToken).ConfigureAwait(false);

        var jsonBytes = memoryStream.ToArray();
        return await client.SendAsync(jsonBytes, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 安全发送文本消息（带输入验证）
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <param name="text">文本内容</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public static async Task<bool> SendTextWithValidationAsync(this ITcpClient client, string text, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(client);
        ArgumentNullException.ThrowIfNull(text);

        // 验证文本消息
        var validation = InputValidator.ValidateTextMessage(text, client.Configuration);
        if (!validation.IsValid)
        {
            var errors = string.Join("; ", validation.Errors);
            throw new ArgumentException($"文本消息验证失败: {errors}");
        }

        return await client.SendTextAsync(text, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 安全发送JSON消息（带输入验证）
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <param name="json">JSON字符串</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public static async Task<bool> SendJsonWithValidationAsync(this ITcpClient client, string json, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(client);
        ArgumentNullException.ThrowIfNull(json);

        // 验证JSON消息
        var validation = InputValidator.ValidateJsonMessage(json, client.Configuration);
        if (!validation.IsValid)
        {
            var errors = string.Join("; ", validation.Errors);
            throw new ArgumentException($"JSON消息验证失败: {errors}");
        }

        return await client.SendTextAsync(json, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 接收JSON对象
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="client">TCP客户端</param>
    /// <param name="options">JSON反序列化选项</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收的对象</returns>
    public static async Task<T?> ReceiveJsonAsync<T>(this ITcpClient client, JsonSerializerOptions? options = null, TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(client);

        var json = await client.ReceiveTextAsync(timeout, cancellationToken).ConfigureAwait(false);
        if (string.IsNullOrEmpty(json))
        {
            return default;
        }

        try
        {
            return JsonSerializer.Deserialize<T>(json, options);
        }
        catch (JsonException)
        {
            return default;
        }
    }

    /// <summary>
    /// 发送文件
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <param name="filePath">文件路径</param>
    /// <param name="bufferSize">缓冲区大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public static async Task<bool> SendFileAsync(this ITcpClient client, string filePath, int bufferSize = 8192, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(client);
        ArgumentNullException.ThrowIfNull(filePath);

        if (!File.Exists(filePath))
        {
            throw new FileNotFoundException($"文件不存在：{filePath}");
        }

        try
        {
            using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            var buffer = new byte[bufferSize];
            int bytesRead;

            while ((bytesRead = await fileStream.ReadAsync(buffer, cancellationToken).ConfigureAwait(false)) > 0)
            {
                var data = new byte[bytesRead];
                Array.Copy(buffer, data, bytesRead);

                if (!await client.SendAsync(data, cancellationToken).ConfigureAwait(false))
                {
                    return false;
                }
            }

            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 接收文件
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <param name="filePath">文件路径</param>
    /// <param name="fileSize">文件大小</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收任务</returns>
    public static async Task<bool> ReceiveFileAsync(this ITcpClient client, string filePath, long fileSize, TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(client);
        ArgumentNullException.ThrowIfNull(filePath);

        if (fileSize <= 0)
        {
            throw new ArgumentException("文件大小必须大于0", nameof(fileSize));
        }

        try
        {
            using var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write);
            long totalReceived = 0;

            while (totalReceived < fileSize)
            {
                var data = await client.ReceiveAsync(timeout, cancellationToken).ConfigureAwait(false);
                if (data == null || data.Length == 0)
                {
                    return false;
                }

                await fileStream.WriteAsync(data, cancellationToken).ConfigureAwait(false);
                totalReceived += data.Length;
            }

            return true;
        }
        catch
        {
            // 删除部分接收的文件
            try
            {
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
            catch
            {
                // 忽略删除错误
            }
            return false;
        }
    }

    /// <summary>
    /// 批量发送消息
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <param name="messages">消息列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public static async Task<int> SendBatchAsync(this ITcpClient client, IEnumerable<TcpMessage> messages, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(client);
        ArgumentNullException.ThrowIfNull(messages);

        return await SendBatchAsync(client, messages, maxConcurrency: Environment.ProcessorCount, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 批量发送消息（高性能版本）
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <param name="messages">消息列表</param>
    /// <param name="maxConcurrency">最大并发数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public static async Task<int> SendBatchAsync(this ITcpClient client, IEnumerable<TcpMessage> messages, int maxConcurrency, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(client);
        ArgumentNullException.ThrowIfNull(messages);

        if (maxConcurrency <= 0)
        {
            throw new ArgumentException("最大并发数必须大于0", nameof(maxConcurrency));
        }

        var messageList = messages.ToList();
        if (messageList.Count == 0)
        {
            return 0;
        }

        // 对于少量消息，使用顺序发送避免并发开销
        if (messageList.Count <= 3)
        {
            var successCount = 0;
            foreach (var message in messageList)
            {
                if (await client.SendMessageAsync(message, cancellationToken).ConfigureAwait(false))
                {
                    successCount++;
                }
            }
            return successCount;
        }

        // 使用高性能批量处理器
        var successCounter = 0;
        var batchSize = Math.Max(10, messageList.Count / maxConcurrency);

        await BatchOptimizer.ProcessBatchAsync(
            messageList,
            batchSize,
            async (batch, ct) =>
            {
                var batchTasks = batch.Select(async message =>
                {
                    if (await client.SendMessageAsync(message, ct).ConfigureAwait(false))
                    {
                        Interlocked.Increment(ref successCounter);
                    }
                });

                await Task.WhenAll(batchTasks).ConfigureAwait(false);
            },
            maxConcurrency,
            cancellationToken).ConfigureAwait(false);

        return successCounter;
    }

    /// <summary>
    /// 批量发送文本
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <param name="texts">文本列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public static async Task<int> SendBatchTextAsync(this ITcpClient client, IEnumerable<string> texts, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(client);
        ArgumentNullException.ThrowIfNull(texts);

        return await SendBatchTextAsync(client, texts, maxConcurrency: Environment.ProcessorCount, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 批量发送文本（高性能版本）
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <param name="texts">文本列表</param>
    /// <param name="maxConcurrency">最大并发数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public static async Task<int> SendBatchTextAsync(this ITcpClient client, IEnumerable<string> texts, int maxConcurrency, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(client);
        ArgumentNullException.ThrowIfNull(texts);

        if (maxConcurrency <= 0)
        {
            throw new ArgumentException("最大并发数必须大于0", nameof(maxConcurrency));
        }

        var textList = texts.ToList();
        if (textList.Count == 0)
        {
            return 0;
        }

        // 对于少量文本，使用顺序发送避免并发开销
        if (textList.Count <= 3)
        {
            var successCount = 0;
            foreach (var text in textList)
            {
                if (await client.SendTextAsync(text, cancellationToken).ConfigureAwait(false))
                {
                    successCount++;
                }
            }
            return successCount;
        }

        // 使用信号量控制并发数量
        using var semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
        var successCounter = 0;

        var tasks = textList.Select(async text =>
        {
            await semaphore.WaitAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                if (await client.SendTextAsync(text, cancellationToken).ConfigureAwait(false))
                {
                    Interlocked.Increment(ref successCounter);
                }
            }
            finally
            {
                semaphore.Release();
            }
        });

        await Task.WhenAll(tasks).ConfigureAwait(false);
        return successCounter;
    }

    /// <summary>
    /// 等待连接建立
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>等待任务</returns>
    public static async Task<bool> WaitForConnectionAsync(this ITcpClient client, TimeSpan timeout, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(client);

        if (client.IsConnected)
        {
            return true;
        }

        using var timeoutCts = new CancellationTokenSource(timeout);
        using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

        var tcs = new TaskCompletionSource<bool>();
        
        void OnConnected(object? sender, Events.ConnectedEventArgs e)
        {
            tcs.TrySetResult(true);
        }

        void OnError(object? sender, Events.TcpClientErrorEventArgs e)
        {
            tcs.TrySetResult(false);
        }

        client.Connected += OnConnected;
        client.Error += OnError;

        try
        {
            var timeoutTask = Task.Delay(timeout, combinedCts.Token);
            var completedTask = await Task.WhenAny(tcs.Task, timeoutTask).ConfigureAwait(false);
            
            if (completedTask == timeoutTask)
            {
                return false;
            }

            return await tcs.Task.ConfigureAwait(false);
        }
        finally
        {
            client.Connected -= OnConnected;
            client.Error -= OnError;
        }
    }

    /// <summary>
    /// 等待特定消息
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <param name="predicate">消息匹配条件</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>匹配的消息</returns>
    public static async Task<TcpMessage?> WaitForMessageAsync(this ITcpClient client, Func<TcpMessage, bool> predicate, TimeSpan timeout, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(client);
        ArgumentNullException.ThrowIfNull(predicate);

        using var timeoutCts = new CancellationTokenSource(timeout);
        using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

        var tcs = new TaskCompletionSource<TcpMessage?>();
        
        void OnMessageReceived(object? sender, Events.MessageReceivedEventArgs e)
        {
            if (predicate(e.Message))
            {
                tcs.TrySetResult(e.Message);
            }
        }

        client.MessageReceived += OnMessageReceived;

        try
        {
            var timeoutTask = Task.Delay(timeout, combinedCts.Token);
            var completedTask = await Task.WhenAny(tcs.Task, timeoutTask).ConfigureAwait(false);
            
            if (completedTask == timeoutTask)
            {
                tcs.TrySetResult(null);
            }

            return await tcs.Task.ConfigureAwait(false);
        }
        finally
        {
            client.MessageReceived -= OnMessageReceived;
        }
    }

    /// <summary>
    /// 发送请求并等待响应
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <param name="request">请求消息</param>
    /// <param name="responseFilter">响应过滤器</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应消息</returns>
    public static async Task<TcpMessage?> SendRequestAsync(this ITcpClient client, TcpMessage request, Func<TcpMessage, bool> responseFilter, TimeSpan timeout, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(client);
        ArgumentNullException.ThrowIfNull(request);
        ArgumentNullException.ThrowIfNull(responseFilter);

        // 发送请求
        if (!await client.SendMessageAsync(request, cancellationToken).ConfigureAwait(false))
        {
            return null;
        }

        // 等待响应
        return await client.WaitForMessageAsync(responseFilter, timeout, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 获取连接信息摘要
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <returns>连接信息摘要</returns>
    public static string GetConnectionSummary(this ITcpClient client)
    {
        ArgumentNullException.ThrowIfNull(client);

        var connectionInfo = client.ConnectionInfo;
        if (connectionInfo == null)
        {
            return "未连接";
        }

        return StringOptimizer.BuildString(sb =>
        {
            sb.Append("连接ID: ").AppendLine(connectionInfo.Id[..8]);
            sb.Append("状态: ").AppendLine(connectionInfo.State);
            sb.Append("远程地址: ").AppendLine(connectionInfo.RemoteEndPoint?.ToString());
            sb.Append("本地地址: ").AppendLine(connectionInfo.LocalEndPoint?.ToString());
            sb.Append("连接时间: ").AppendLine(connectionInfo.ConnectedAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? "N/A");
            sb.Append("持续时间: ").AppendLine(connectionInfo.Duration?.ToString(@"hh\:mm\:ss") ?? "N/A");
            sb.Append("SSL启用: ").AppendLine(connectionInfo.IsSslEnabled ? "是" : "否");
            sb.Append("重连次数: ").AppendLine(connectionInfo.ReconnectCount.ToString());

            var stats = connectionInfo.Statistics;
            sb.Append("发送字节: ").AppendLine(stats.BytesSent.ToString("N0"));
            sb.Append("接收字节: ").AppendLine(stats.BytesReceived.ToString("N0"));
            sb.Append("发送消息: ").AppendLine(stats.MessagesSent.ToString("N0"));
            sb.Append("接收消息: ").AppendLine(stats.MessagesReceived.ToString("N0"));
            sb.Append("平均延迟: ").Append(stats.AverageLatency.ToString("F2")).AppendLine("ms");
        });
    }

    /// <summary>
    /// 获取性能指标
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <returns>性能指标</returns>
    public static PerformanceMetrics GetPerformanceMetrics(this ITcpClient client)
    {
        ArgumentNullException.ThrowIfNull(client);

        var statistics = client.GetStatistics();
        var connectionInfo = client.ConnectionInfo;

        var metrics = new PerformanceMetrics();

        if (connectionInfo != null)
        {
            metrics.NetworkLatency = connectionInfo.Statistics.AverageLatency;
            metrics.ConnectionQuality = client.GetConnectionQuality();
        }

        if (statistics.Uptime.HasValue && statistics.Uptime.Value.TotalSeconds > 0)
        {
            metrics.Throughput = (statistics.TotalBytesSent + statistics.TotalBytesReceived) / statistics.Uptime.Value.TotalSeconds;
        }

        return metrics;
    }

    /// <summary>
    /// 检查是否支持功能
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <param name="feature">功能名称</param>
    /// <returns>是否支持</returns>
    public static bool SupportsFeature(this ITcpClient client, string feature)
    {
        ArgumentNullException.ThrowIfNull(client);
        ArgumentNullException.ThrowIfNull(feature);

        return feature.ToLowerInvariant() switch
        {
            "ssl" => client.Configuration.EnableSsl,
            "heartbeat" => client.Configuration.EnableHeartbeat,
            "autoreconnect" => client.Configuration.EnableAutoReconnect,
            "connectionpool" => client.Configuration.ConnectionPoolConfig?.Enabled == true,
            "statistics" => client.Configuration.EnableStatistics,
            _ => false
        };
    }

    /// <summary>
    /// 创建连接诊断报告
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <returns>诊断报告</returns>
    public static async Task<string> CreateDiagnosticReportAsync(this ITcpClient client)
    {
        ArgumentNullException.ThrowIfNull(client);

        var sb = new StringBuilder();
        sb.AppendLine("=== TCP客户端诊断报告 ===");
        sb.AppendLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        sb.AppendLine();

        // 基本信息
        sb.AppendLine("--- 基本信息 ---");
        sb.AppendLine($"目标服务器: {client.Configuration.Host}:{client.Configuration.Port}");
        sb.AppendLine($"客户端ID: {client.Configuration.ClientId ?? "N/A"}");
        sb.AppendLine($"客户端名称: {client.Configuration.ClientName ?? "N/A"}");
        sb.AppendLine();

        // 连接状态
        sb.AppendLine("--- 连接状态 ---");
        sb.AppendLine($"当前状态: {client.Status}");
        sb.AppendLine($"是否连接: {(client.IsConnected ? "是" : "否")}");
        sb.AppendLine($"连接时间: {client.ConnectedAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? "N/A"}");
        sb.AppendLine($"连接持续时间: {client.ConnectionDuration?.ToString(@"hh\:mm\:ss") ?? "N/A"}");
        sb.AppendLine();

        // 功能支持
        sb.AppendLine("--- 功能支持 ---");
        sb.AppendLine($"SSL/TLS: {(client.SupportsFeature("ssl") ? "启用" : "禁用")}");
        sb.AppendLine($"心跳检测: {(client.SupportsFeature("heartbeat") ? "启用" : "禁用")}");
        sb.AppendLine($"自动重连: {(client.SupportsFeature("autoreconnect") ? "启用" : "禁用")}");
        sb.AppendLine($"连接池: {(client.SupportsFeature("connectionpool") ? "启用" : "禁用")}");
        sb.AppendLine($"统计信息: {(client.SupportsFeature("statistics") ? "启用" : "禁用")}");
        sb.AppendLine();

        // 性能指标
        sb.AppendLine("--- 性能指标 ---");
        var metrics = client.GetPerformanceMetrics();
        sb.AppendLine($"网络延迟: {metrics.NetworkLatency:F2}ms");
        sb.AppendLine($"吞吐量: {metrics.Throughput:F2} bytes/s");
        sb.AppendLine($"连接质量: {metrics.ConnectionQuality:F1}%");
        sb.AppendLine();

        // 统计信息
        sb.AppendLine("--- 统计信息 ---");
        var statistics = client.GetStatistics();
        sb.AppendLine($"总连接次数: {statistics.TotalConnections}");
        sb.AppendLine($"成功连接次数: {statistics.SuccessfulConnections}");
        sb.AppendLine($"连接成功率: {statistics.ConnectionSuccessRate:F1}%");
        sb.AppendLine($"总重连次数: {statistics.TotalReconnections}");
        sb.AppendLine($"重连成功率: {statistics.ReconnectionSuccessRate:F1}%");
        sb.AppendLine($"发送字节数: {statistics.TotalBytesSent:N0}");
        sb.AppendLine($"接收字节数: {statistics.TotalBytesReceived:N0}");
        sb.AppendLine($"发送消息数: {statistics.TotalMessagesSent:N0}");
        sb.AppendLine($"接收消息数: {statistics.TotalMessagesReceived:N0}");
        sb.AppendLine($"错误总数: {statistics.TotalErrors}");
        sb.AppendLine($"错误率: {statistics.ErrorRate:F2}%");
        sb.AppendLine();

        // 健康检查
        sb.AppendLine("--- 健康检查 ---");
        try
        {
            var isHealthy = await client.CheckHealthAsync().ConfigureAwait(false);
            sb.AppendLine($"健康状态: {(isHealthy ? "健康" : "异常")}");
            
            if (client.IsConnected)
            {
                var latency = await client.PingAsync().ConfigureAwait(false);
                sb.AppendLine($"当前延迟: {(latency?.ToString("F2") ?? "N/A")}ms");
            }
        }
        catch (Exception ex)
        {
            sb.AppendLine($"健康检查失败: {ex.Message}");
        }

        sb.AppendLine();
        sb.AppendLine("=== 报告结束 ===");

        return sb.ToString();
    }
}
