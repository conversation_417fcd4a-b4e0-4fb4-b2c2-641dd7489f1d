using System.Text;
using Microsoft.Extensions.Logging;
using Liam.TcpClient.Constants;
using Liam.TcpClient.Events;
using Liam.TcpClient.Exceptions;
using Liam.TcpClient.Interfaces;
using Liam.TcpClient.Models;

namespace Liam.TcpClient.Services;

/// <summary>
/// TCP客户端实现
/// </summary>
public class TcpClient : ITcpClient
{
    private readonly ILogger<TcpClient> _logger;
    private readonly IConnectionManager _connectionManager;
    private readonly IMessageHandler _messageHandler;
    private readonly IHeartbeatManager _heartbeatManager;
    private readonly ClientStatistics _statistics = new();
    private readonly TcpClientConfig _configuration;
    private bool _disposed;

    /// <summary>
    /// 客户端配置
    /// </summary>
    public TcpClientConfig Configuration => _configuration;

    /// <summary>
    /// 连接信息
    /// </summary>
    public ConnectionInfo? ConnectionInfo => _connectionManager.ConnectionInfo;

    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected => _connectionManager.IsConnected;

    /// <summary>
    /// 是否正在连接
    /// </summary>
    public bool IsConnecting => _connectionManager.IsConnecting;

    /// <summary>
    /// 是否正在重连
    /// </summary>
    public bool IsReconnecting => _connectionManager.IsReconnecting;

    /// <summary>
    /// 客户端状态
    /// </summary>
    public string Status => ConnectionInfo?.State ?? TcpClientConstants.ConnectionStates.Disconnected;

    /// <summary>
    /// 连接建立时间
    /// </summary>
    public DateTime? ConnectedAt => ConnectionInfo?.ConnectedAt;

    /// <summary>
    /// 连接持续时间
    /// </summary>
    public TimeSpan? ConnectionDuration => ConnectionInfo?.Duration;

    /// <summary>
    /// 连接状态变更事件
    /// </summary>
    public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    /// <summary>
    /// 连接建立事件
    /// </summary>
    public event EventHandler<ConnectedEventArgs>? Connected;

    /// <summary>
    /// 连接断开事件
    /// </summary>
    public event EventHandler<DisconnectedEventArgs>? Disconnected;

    /// <summary>
    /// 数据接收事件
    /// </summary>
    public event EventHandler<DataReceivedEventArgs>? DataReceived;

    /// <summary>
    /// 数据发送事件
    /// </summary>
    public event EventHandler<DataSentEventArgs>? DataSent;

    /// <summary>
    /// 消息接收事件
    /// </summary>
    public event EventHandler<MessageReceivedEventArgs>? MessageReceived;

    /// <summary>
    /// 消息发送事件
    /// </summary>
    public event EventHandler<MessageSentEventArgs>? MessageSent;

    /// <summary>
    /// 心跳事件
    /// </summary>
    public event EventHandler<HeartbeatEventArgs>? Heartbeat;

    /// <summary>
    /// 错误事件
    /// </summary>
    public event EventHandler<TcpClientErrorEventArgs>? Error;

    /// <summary>
    /// 初始化TCP客户端
    /// </summary>
    /// <param name="configuration">客户端配置</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="connectionManager">连接管理器</param>
    /// <param name="messageHandler">消息处理器</param>
    /// <param name="heartbeatManager">心跳管理器</param>
    public TcpClient(
        TcpClientConfig configuration,
        ILogger<TcpClient> logger,
        IConnectionManager connectionManager,
        IMessageHandler messageHandler,
        IHeartbeatManager heartbeatManager)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        _messageHandler = messageHandler ?? throw new ArgumentNullException(nameof(messageHandler));
        _heartbeatManager = heartbeatManager ?? throw new ArgumentNullException(nameof(heartbeatManager));

        // 验证配置
        var validation = _configuration.Validate();
        if (!validation.IsValid)
        {
            throw new ArgumentException($"配置无效：{string.Join(", ", validation.Errors)}", nameof(configuration));
        }

        // 初始化统计信息
        _statistics.StartedAt = DateTime.UtcNow;

        // 订阅事件
        SubscribeToEvents();

        _logger.LogInformation("TCP客户端已初始化，目标：{Host}:{Port}", _configuration.Host, _configuration.Port);
    }

    /// <summary>
    /// 连接到服务器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接任务</returns>
    public async Task<bool> ConnectAsync(CancellationToken cancellationToken = default)
    {
        return await ConnectAsync(_configuration.Host, _configuration.Port, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 连接到指定服务器
    /// </summary>
    /// <param name="host">服务器主机</param>
    /// <param name="port">服务器端口</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接任务</returns>
    public async Task<bool> ConnectAsync(string host, int port, CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(TcpClient));
        }

        try
        {
            _logger.LogInformation("开始连接到 {Host}:{Port}", host, port);

            // 连接到服务器
            var connected = await _connectionManager.ConnectAsync(host, port, _configuration, cancellationToken).ConfigureAwait(false);
            
            if (connected)
            {
                // 启动消息处理
                if (_connectionManager.Stream != null)
                {
                    await _messageHandler.StartAsync(_connectionManager.Stream, cancellationToken).ConfigureAwait(false);
                }

                // 启动心跳检测
                if (_configuration.EnableHeartbeat)
                {
                    _heartbeatManager.Interval = TimeSpan.FromSeconds(_configuration.HeartbeatIntervalSeconds);
                    _heartbeatManager.Timeout = TimeSpan.FromSeconds(_configuration.HeartbeatTimeoutSeconds);
                    await _heartbeatManager.StartAsync(cancellationToken).ConfigureAwait(false);
                }

                _statistics.RecordConnectionAttempt(true);
                _logger.LogInformation("成功连接到 {Host}:{Port}", host, port);
            }
            else
            {
                _statistics.RecordConnectionAttempt(false);
                _logger.LogWarning("连接到 {Host}:{Port} 失败", host, port);
            }

            return connected;
        }
        catch (Exception ex)
        {
            _statistics.RecordConnectionAttempt(false);
            _statistics.RecordError();
            _logger.LogError(ex, "连接到 {Host}:{Port} 时发生错误", host, port);
            Error?.Invoke(this, new TcpClientErrorEventArgs(ex, ConnectionInfo));
            throw;
        }
    }

    /// <summary>
    /// 断开连接
    /// </summary>
    /// <param name="reason">断开原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>断开任务</returns>
    public async Task DisconnectAsync(string? reason = null, CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            return;
        }

        try
        {
            _logger.LogInformation("开始断开连接，原因：{Reason}", reason ?? "用户请求");

            // 停止心跳检测
            if (_heartbeatManager.IsRunning)
            {
                await _heartbeatManager.StopAsync(cancellationToken).ConfigureAwait(false);
            }

            // 停止消息处理
            if (_messageHandler.IsRunning)
            {
                await _messageHandler.StopAsync(cancellationToken).ConfigureAwait(false);
            }

            // 断开连接
            await _connectionManager.DisconnectAsync(reason, cancellationToken).ConfigureAwait(false);

            _logger.LogInformation("连接已断开");
        }
        catch (Exception ex)
        {
            _statistics.RecordError();
            _logger.LogError(ex, "断开连接时发生错误");
            Error?.Invoke(this, new TcpClientErrorEventArgs(ex, ConnectionInfo));
            throw;
        }
    }

    /// <summary>
    /// 重新连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重连任务</returns>
    public async Task<bool> ReconnectAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(TcpClient));
        }

        try
        {
            _logger.LogInformation("开始重新连接");

            var reconnected = await _connectionManager.ReconnectAsync(cancellationToken).ConfigureAwait(false);
            
            if (reconnected)
            {
                // 重新启动消息处理
                if (_connectionManager.Stream != null)
                {
                    await _messageHandler.StartAsync(_connectionManager.Stream, cancellationToken).ConfigureAwait(false);
                }

                // 重新启动心跳检测
                if (_configuration.EnableHeartbeat)
                {
                    await _heartbeatManager.StartAsync(cancellationToken).ConfigureAwait(false);
                }

                _statistics.RecordReconnectionAttempt(true);
                _logger.LogInformation("重新连接成功");
            }
            else
            {
                _statistics.RecordReconnectionAttempt(false);
                _logger.LogWarning("重新连接失败");
            }

            return reconnected;
        }
        catch (Exception ex)
        {
            _statistics.RecordReconnectionAttempt(false);
            _statistics.RecordError();
            _logger.LogError(ex, "重新连接时发生错误");
            Error?.Invoke(this, new TcpClientErrorEventArgs(ex, ConnectionInfo));
            throw;
        }
    }

    /// <summary>
    /// 发送数据
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public async Task<bool> SendAsync(byte[] data, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(data);

        if (!IsConnected)
        {
            throw new InvalidOperationException(TcpClientConstants.ErrorMessages.ClientNotConnected);
        }

        try
        {
            var success = await _messageHandler.SendAsync(data, cancellationToken).ConfigureAwait(false);
            
            if (success)
            {
                _statistics.RecordDataSent(data.Length);
                ConnectionInfo?.Statistics.RecordSent(data.Length);
                DataSent?.Invoke(this, new DataSentEventArgs(ConnectionInfo!, data, true));
            }
            else
            {
                _statistics.RecordError();
                DataSent?.Invoke(this, new DataSentEventArgs(ConnectionInfo!, data, false));
            }

            return success;
        }
        catch (Exception ex)
        {
            _statistics.RecordError();
            _logger.LogError(ex, "发送数据时发生错误，数据长度：{Length}", data.Length);
            Error?.Invoke(this, new TcpClientErrorEventArgs(ex, ConnectionInfo));
            throw;
        }
    }

    /// <summary>
    /// 发送文本
    /// </summary>
    /// <param name="text">文本</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public async Task<bool> SendTextAsync(string text, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(text);
        
        var data = Encoding.UTF8.GetBytes(text);
        return await SendAsync(data, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 发送消息
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public async Task<bool> SendMessageAsync(TcpMessage message, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(message);

        if (!IsConnected)
        {
            throw new InvalidOperationException(TcpClientConstants.ErrorMessages.ClientNotConnected);
        }

        try
        {
            var success = await _messageHandler.SendMessageAsync(message, cancellationToken).ConfigureAwait(false);
            
            if (success)
            {
                _statistics.RecordDataSent(message.Length);
                ConnectionInfo?.Statistics.RecordSent(message.Length);
                MessageSent?.Invoke(this, new MessageSentEventArgs(ConnectionInfo!, message, true));
            }
            else
            {
                _statistics.RecordError();
                MessageSent?.Invoke(this, new MessageSentEventArgs(ConnectionInfo!, message, false));
            }

            return success;
        }
        catch (Exception ex)
        {
            _statistics.RecordError();
            _logger.LogError(ex, "发送消息时发生错误：{Message}", message);
            Error?.Invoke(this, new TcpClientErrorEventArgs(ex, ConnectionInfo));
            throw;
        }
    }

    /// <summary>
    /// 发送心跳
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public async Task<bool> SendHeartbeatAsync(CancellationToken cancellationToken = default)
    {
        if (!IsConnected)
        {
            return false;
        }

        try
        {
            var success = await _heartbeatManager.SendHeartbeatAsync(cancellationToken).ConfigureAwait(false);
            
            if (success)
            {
                _statistics.RecordHeartbeatSent();
            }

            return success;
        }
        catch (Exception ex)
        {
            _statistics.RecordError();
            _logger.LogError(ex, "发送心跳时发生错误");
            Error?.Invoke(this, new TcpClientErrorEventArgs(ex, ConnectionInfo));
            return false;
        }
    }

    /// <summary>
    /// 等待接收数据
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收的数据</returns>
    public async Task<byte[]?> ReceiveAsync(TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        if (!IsConnected)
        {
            return null;
        }

        try
        {
            return await _messageHandler.ReceiveAsync(timeout, cancellationToken).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _statistics.RecordError();
            _logger.LogError(ex, "接收数据时发生错误");
            Error?.Invoke(this, new TcpClientErrorEventArgs(ex, ConnectionInfo));
            return null;
        }
    }

    /// <summary>
    /// 等待接收文本
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收的文本</returns>
    public async Task<string?> ReceiveTextAsync(TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        var data = await ReceiveAsync(timeout, cancellationToken).ConfigureAwait(false);
        return data != null ? Encoding.UTF8.GetString(data) : null;
    }

    /// <summary>
    /// 等待接收消息
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收的消息</returns>
    public async Task<TcpMessage?> ReceiveMessageAsync(TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        if (!IsConnected)
        {
            return null;
        }

        try
        {
            return await _messageHandler.ReceiveMessageAsync(timeout, cancellationToken).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _statistics.RecordError();
            _logger.LogError(ex, "接收消息时发生错误");
            Error?.Invoke(this, new TcpClientErrorEventArgs(ex, ConnectionInfo));
            return null;
        }
    }

    /// <summary>
    /// 获取客户端统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public ClientStatistics GetStatistics()
    {
        _statistics.CurrentConnection = ConnectionInfo;
        _statistics.Update();
        return _statistics;
    }

    /// <summary>
    /// 重置统计信息
    /// </summary>
    public void ResetStatistics()
    {
        _statistics.Reset();
        ConnectionInfo?.Statistics.Reset();
        _heartbeatManager.ResetStatistics();
        _logger.LogInformation("统计信息已重置");
    }

    /// <summary>
    /// 测试连接延迟
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>延迟时间（毫秒）</returns>
    public async Task<double?> PingAsync(CancellationToken cancellationToken = default)
    {
        if (!IsConnected)
        {
            return null;
        }

        try
        {
            return await _connectionManager.GetLatencyAsync(cancellationToken).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "测试连接延迟时发生错误");
            return null;
        }
    }

    /// <summary>
    /// 启用自动重连
    /// </summary>
    public void EnableAutoReconnect()
    {
        _connectionManager.EnableAutoReconnect();
        _logger.LogInformation("已启用自动重连");
    }

    /// <summary>
    /// 禁用自动重连
    /// </summary>
    public void DisableAutoReconnect()
    {
        _connectionManager.DisableAutoReconnect();
        _logger.LogInformation("已禁用自动重连");
    }

    /// <summary>
    /// 启用心跳检测
    /// </summary>
    public void EnableHeartbeat()
    {
        if (IsConnected && !_heartbeatManager.IsRunning)
        {
            _ = Task.Run(async () => await _heartbeatManager.StartAsync().ConfigureAwait(false));
        }
        _logger.LogInformation("已启用心跳检测");
    }

    /// <summary>
    /// 禁用心跳检测
    /// </summary>
    public void DisableHeartbeat()
    {
        if (_heartbeatManager.IsRunning)
        {
            _ = Task.Run(async () => await _heartbeatManager.StopAsync().ConfigureAwait(false));
        }
        _logger.LogInformation("已禁用心跳检测");
    }

    /// <summary>
    /// 获取连接质量评分
    /// </summary>
    /// <returns>质量评分（0-100）</returns>
    public double GetConnectionQuality()
    {
        return _connectionManager.GetConnectionQuality();
    }

    /// <summary>
    /// 检查连接健康状态
    /// </summary>
    /// <returns>是否健康</returns>
    public async Task<bool> CheckHealthAsync()
    {
        if (!IsConnected)
        {
            return false;
        }

        try
        {
            // 检查连接状态
            var connectionOk = _connectionManager.CheckConnection();
            
            // 测试延迟
            var latency = await PingAsync().ConfigureAwait(false);
            var latencyOk = latency.HasValue && latency.Value < 5000; // 5秒内响应

            // 检查心跳状态
            var heartbeatOk = !_configuration.EnableHeartbeat || 
                             (_heartbeatManager.LastResponseAt.HasValue && 
                              DateTime.UtcNow - _heartbeatManager.LastResponseAt.Value < TimeSpan.FromSeconds(_configuration.HeartbeatTimeoutSeconds * 2));

            return connectionOk && latencyOk && heartbeatOk;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 刷新连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>刷新任务</returns>
    public async Task RefreshConnectionAsync(CancellationToken cancellationToken = default)
    {
        if (!IsConnected)
        {
            return;
        }

        try
        {
            await _connectionManager.RefreshAsync(cancellationToken).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新连接时发生错误");
            Error?.Invoke(this, new TcpClientErrorEventArgs(ex, ConnectionInfo));
        }
    }

    /// <summary>
    /// 订阅事件
    /// </summary>
    private void SubscribeToEvents()
    {
        // 连接管理器事件
        _connectionManager.StateChanged += OnConnectionStateChanged;
        _connectionManager.Connected += OnConnected;
        _connectionManager.Disconnected += OnDisconnected;

        // 消息处理器事件
        _messageHandler.MessageReceived += OnMessageReceived;
        _messageHandler.DataReceived += OnDataReceived;
        _messageHandler.Error += OnMessageHandlerError;

        // 心跳管理器事件
        _heartbeatManager.HeartbeatSent += OnHeartbeatSent;
        _heartbeatManager.HeartbeatReceived += OnHeartbeatReceived;
        _heartbeatManager.HeartbeatTimeout += OnHeartbeatTimeout;
    }

    /// <summary>
    /// 连接状态变更事件处理
    /// </summary>
    private void OnConnectionStateChanged(object? sender, string newState)
    {
        if (ConnectionInfo != null)
        {
            var oldState = ConnectionInfo.State;
            ConnectionStateChanged?.Invoke(this, new ConnectionStateChangedEventArgs(ConnectionInfo, oldState, newState));
        }
    }

    /// <summary>
    /// 连接建立事件处理
    /// </summary>
    private void OnConnected(object? sender, ConnectionInfo connectionInfo)
    {
        Connected?.Invoke(this, new ConnectedEventArgs(connectionInfo, connectionInfo.ReconnectCount > 0));
    }

    /// <summary>
    /// 连接断开事件处理
    /// </summary>
    private void OnDisconnected(object? sender, (ConnectionInfo ConnectionInfo, string? Reason, Exception? Exception) args)
    {
        Disconnected?.Invoke(this, new DisconnectedEventArgs(args.ConnectionInfo, args.Reason, args.Exception != null, args.Exception));
    }

    /// <summary>
    /// 消息接收事件处理
    /// </summary>
    private void OnMessageReceived(object? sender, TcpMessage message)
    {
        if (ConnectionInfo != null)
        {
            _statistics.RecordDataReceived(message.Length);
            ConnectionInfo.Statistics.RecordReceived(message.Length);

            // 处理心跳响应
            if (message.MessageType == TcpClientConstants.MessageTypes.HeartbeatResponse)
            {
                _heartbeatManager.HandleHeartbeatResponse(message);
                _statistics.RecordHeartbeatReceived();
            }

            MessageReceived?.Invoke(this, new MessageReceivedEventArgs(ConnectionInfo, message));
        }
    }

    /// <summary>
    /// 数据接收事件处理
    /// </summary>
    private void OnDataReceived(object? sender, byte[] data)
    {
        if (ConnectionInfo != null)
        {
            DataReceived?.Invoke(this, new DataReceivedEventArgs(ConnectionInfo, data));
        }
    }

    /// <summary>
    /// 消息处理器错误事件处理
    /// </summary>
    private void OnMessageHandlerError(object? sender, Exception exception)
    {
        _statistics.RecordError();
        Error?.Invoke(this, new TcpClientErrorEventArgs(exception, ConnectionInfo));
    }

    /// <summary>
    /// 心跳发送事件处理
    /// </summary>
    private void OnHeartbeatSent(object? sender, (DateTime Timestamp, double? ResponseTime) args)
    {
        if (ConnectionInfo != null)
        {
            Heartbeat?.Invoke(this, new HeartbeatEventArgs(ConnectionInfo, HeartbeatType.Request, args.ResponseTime));
        }
    }

    /// <summary>
    /// 心跳接收事件处理
    /// </summary>
    private void OnHeartbeatReceived(object? sender, (DateTime Timestamp, double ResponseTime) args)
    {
        if (ConnectionInfo != null)
        {
            ConnectionInfo.Statistics.RecordLatency(args.ResponseTime);
            Heartbeat?.Invoke(this, new HeartbeatEventArgs(ConnectionInfo, HeartbeatType.Response, args.ResponseTime));
        }
    }

    /// <summary>
    /// 心跳超时事件处理
    /// </summary>
    private void OnHeartbeatTimeout(object? sender, DateTime timestamp)
    {
        if (ConnectionInfo != null)
        {
            _statistics.RecordHeartbeatTimeout();
            Heartbeat?.Invoke(this, new HeartbeatEventArgs(ConnectionInfo, HeartbeatType.Timeout));
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
        {
            return;
        }

        _disposed = true;

        try
        {
            DisconnectAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "释放TCP客户端时发生错误");
        }

        _connectionManager.Dispose();
        _messageHandler.Dispose();
        _heartbeatManager.Dispose();

        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 异步释放资源
    /// </summary>
    public async ValueTask DisposeAsync()
    {
        if (_disposed)
        {
            return;
        }

        _disposed = true;

        try
        {
            await DisconnectAsync().ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "异步释放TCP客户端时发生错误");
        }

        await _connectionManager.DisposeAsync().ConfigureAwait(false);
        await _messageHandler.DisposeAsync().ConfigureAwait(false);
        await _heartbeatManager.DisposeAsync().ConfigureAwait(false);

        GC.SuppressFinalize(this);
    }
}
