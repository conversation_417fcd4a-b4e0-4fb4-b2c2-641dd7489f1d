using System.Net.Sockets;
using System.Net.Sockets;
using FluentAssertions;
using Xunit;
using Liam.TcpClient.Exceptions;
using Liam.TcpClient.Models;
using SystemAuthException = System.Security.Authentication.AuthenticationException;

namespace Liam.TcpClient.Tests.Exceptions;

/// <summary>
/// 异常处理测试
/// </summary>
public class ExceptionHandlingTests
{
    [Fact]
    public void TcpClientExceptionBase_ShouldProvideDetailedContext()
    {
        // Arrange
        var config = new TcpClientConfig
        {
            Host = "test.example.com",
            Port = 8080,
            ClientId = "test-client-123",
            ClientName = "Test Client"
        };

        var context = new Dictionary<string, object>
        {
            ["Operation"] = "Connect",
            ["Attempt"] = 1
        };

        var innerException = new InvalidOperationException("Inner error");

        // Act
        var exception = new ConnectionException(
            "Connection failed",
            innerException,
            "Connecting",
            config,
            context);

        // Assert
        exception.Category.Should().Be(TcpClientExceptionCategory.Network);
        exception.Severity.Should().Be(TcpClientExceptionSeverity.Error);
        exception.IsRetryable.Should().BeTrue();
        exception.SuggestedRetryDelay.Should().Be(TimeSpan.FromSeconds(5));
        exception.ConnectionState.Should().Be("Connecting");
        exception.ConfigurationInfo.Should().NotBeNull();
        exception.OperationContext.Should().ContainKey("Operation");
        exception.OperationContext.Should().ContainKey("Attempt");
        exception.OperationContext.Should().ContainKey("ExceptionType");
        exception.OperationContext.Should().ContainKey("Timestamp");
        exception.OperationContext.Should().ContainKey("ThreadId");
    }

    [Fact]
    public void ConnectionException_CreateConnectionFailed_ShouldCreateProperException()
    {
        // Arrange
        var host = "test.example.com";
        var port = 8080;
        var innerException = new SocketException();
        var config = new TcpClientConfig { Host = host, Port = port };

        // Act
        var exception = ConnectionException.CreateConnectionFailed(host, port, innerException, config);

        // Assert
        exception.Message.Should().Contain($"无法连接到服务器 {host}:{port}");
        exception.InnerException.Should().Be(innerException);
        exception.ConnectionState.Should().Be("Disconnected");
        exception.OperationContext.Should().ContainKey("Host");
        exception.OperationContext.Should().ContainKey("Port");
        exception.OperationContext.Should().ContainKey("Operation");
        exception.OperationContext["Host"].Should().Be(host);
        exception.OperationContext["Port"].Should().Be(port);
        exception.OperationContext["Operation"].Should().Be("Connect");
    }

    [Fact]
    public void ConnectionTimeoutException_ShouldCalculateRetryDelay()
    {
        // Arrange
        var timeout = TimeSpan.FromSeconds(30);

        // Act
        var exception = new ConnectionTimeoutException(timeout);

        // Assert
        exception.Category.Should().Be(TcpClientExceptionCategory.Timeout);
        exception.Severity.Should().Be(TcpClientExceptionSeverity.Warning);
        exception.IsRetryable.Should().BeTrue();
        exception.SuggestedRetryDelay.Should().Be(TimeSpan.FromSeconds(15)); // 30 * 0.5
        exception.Timeout.Should().Be(timeout);
        exception.OperationContext.Should().ContainKey("TimeoutSeconds");
        exception.OperationContext["TimeoutSeconds"].Should().Be(30.0);
    }

    [Fact]
    public void ValidationException_ShouldHandleMultipleErrors()
    {
        // Arrange
        var errors = new List<string>
        {
            "Field1 is required",
            "Field2 must be positive",
            "Field3 is too long"
        };

        // Act
        var exception = new ValidationException(errors);

        // Assert
        exception.Category.Should().Be(TcpClientExceptionCategory.Validation);
        exception.Severity.Should().Be(TcpClientExceptionSeverity.Warning);
        exception.IsRetryable.Should().BeFalse();
        exception.ValidationErrors.Should().HaveCount(3);
        exception.ValidationErrors.Should().Contain("Field1 is required");
        exception.ValidationErrors.Should().Contain("Field2 must be positive");
        exception.ValidationErrors.Should().Contain("Field3 is too long");
        exception.Message.Should().Contain("验证失败 (3个错误)");
        exception.OperationContext["ErrorCount"].Should().Be(3);
    }

    [Fact]
    public void SecurityException_ShouldBeCritical()
    {
        // Arrange & Act
        var exception = new SecurityException("SSL certificate validation failed");

        // Assert
        exception.Category.Should().Be(TcpClientExceptionCategory.Security);
        exception.Severity.Should().Be(TcpClientExceptionSeverity.Critical);
        exception.IsRetryable.Should().BeFalse();
        exception.SuggestedRetryDelay.Should().BeNull();
    }

    [Fact]
    public void ProtocolException_ShouldBeRetryable()
    {
        // Arrange & Act
        var exception = new ProtocolException("Invalid message format");

        // Assert
        exception.Category.Should().Be(TcpClientExceptionCategory.Protocol);
        exception.Severity.Should().Be(TcpClientExceptionSeverity.Error);
        exception.IsRetryable.Should().BeTrue();
        exception.SuggestedRetryDelay.Should().Be(TimeSpan.FromSeconds(2));
    }

    [Fact]
    public void ResourceException_ShouldHaveLongerRetryDelay()
    {
        // Arrange & Act
        var exception = new ResourceException("Connection pool exhausted");

        // Assert
        exception.Category.Should().Be(TcpClientExceptionCategory.Resource);
        exception.Severity.Should().Be(TcpClientExceptionSeverity.Error);
        exception.IsRetryable.Should().BeTrue();
        exception.SuggestedRetryDelay.Should().Be(TimeSpan.FromSeconds(10));
    }

    [Fact]
    public void TcpClientExceptionBase_GetDetailedMessage_ShouldIncludeAllContext()
    {
        // Arrange
        var config = new TcpClientConfig
        {
            Host = "test.example.com",
            Port = 8080
        };

        var context = new Dictionary<string, object>
        {
            ["Operation"] = "Connect",
            ["Attempt"] = 1
        };

        var exception = new ConnectionException(
            "Connection failed",
            new SocketException(),
            "Connecting",
            config,
            context);

        // Act
        var detailedMessage = exception.GetDetailedMessage();

        // Assert
        detailedMessage.Should().Contain("异常类型: Network");
        detailedMessage.Should().Contain("严重程度: Error");
        detailedMessage.Should().Contain("发生时间:");
        detailedMessage.Should().Contain("消息: Connection failed");
        detailedMessage.Should().Contain("连接状态: Connecting");
        detailedMessage.Should().Contain("配置信息:");
        detailedMessage.Should().Contain("可重试: 是");
        detailedMessage.Should().Contain("建议重试延迟: 5秒");
        detailedMessage.Should().Contain("操作上下文:");
        detailedMessage.Should().Contain("Operation: Connect");
        detailedMessage.Should().Contain("内部异常: SocketException");
    }

    [Fact]
    public void TcpClientExceptionBase_ShouldMaskSensitiveInformation()
    {
        // Arrange
        var config = new TcpClientConfig
        {
            Host = "secret.internal.com",
            ClientId = "secret-client-id-12345",
            ClientName = "Secret Client Name"
        };

        // Act
        var exception = new ConnectionException("Test", null, null, config);

        // Assert
        exception.ConfigurationInfo.Should().NotBeNull();
        exception.ConfigurationInfo.Should().NotContain("secret.internal.com");
        exception.ConfigurationInfo.Should().NotContain("secret-client-id-12345");
        exception.ConfigurationInfo.Should().NotContain("Secret Client Name");
        exception.ConfigurationInfo.Should().Contain("****"); // 应该包含脱敏标记
    }

    [Fact]
    public void TcpClientExceptionBase_AddContext_ShouldUpdateOperationContext()
    {
        // Arrange
        var exception = new ConnectionException("Test");

        // Act
        exception.AddContext("CustomKey", "CustomValue");
        exception.AddContext("NumericKey", 42);

        // Assert
        exception.OperationContext.Should().ContainKey("CustomKey");
        exception.OperationContext.Should().ContainKey("NumericKey");
        exception.OperationContext["CustomKey"].Should().Be("CustomValue");
        exception.OperationContext["NumericKey"].Should().Be(42);
    }

    [Fact]
    public void AuthenticationException_ShouldNotBeRetryable()
    {
        // Arrange & Act
        var exception = new Liam.TcpClient.Exceptions.AuthenticationException("Authentication failed");

        // Assert
        exception.Category.Should().Be(TcpClientExceptionCategory.Authentication);
        exception.Severity.Should().Be(TcpClientExceptionSeverity.Error);
        exception.IsRetryable.Should().BeFalse();
        exception.SuggestedRetryDelay.Should().BeNull();
    }

    [Fact]
    public void ConfigurationException_ShouldNotBeRetryable()
    {
        // Arrange & Act
        var exception = new ConfigurationException("Invalid configuration");

        // Assert
        exception.Category.Should().Be(TcpClientExceptionCategory.Configuration);
        exception.Severity.Should().Be(TcpClientExceptionSeverity.Error);
        exception.IsRetryable.Should().BeFalse();
        exception.SuggestedRetryDelay.Should().BeNull();
    }

    [Fact]
    public void NetworkException_ShouldHandleSocketExceptions()
    {
        // Arrange
        var socketException = new SocketException((int)SocketError.ConnectionRefused);
        var config = new TcpClientConfig { Host = "localhost", Port = 8080 };

        // Act
        var exception = new ConnectionException("Network error", socketException, "Failed", config);

        // Assert
        exception.Category.Should().Be(TcpClientExceptionCategory.Connection);
        exception.Severity.Should().Be(TcpClientExceptionSeverity.Error);
        exception.IsRetryable.Should().BeTrue();
        exception.InnerException.Should().Be(socketException);
    }

    [Fact]
    public void SecurityException_ShouldHandleAuthenticationFailures()
    {
        // Arrange
        var authException = new SystemAuthException("SSL authentication failed");
        var config = new TcpClientConfig { Host = "secure.example.com", EnableSsl = true };

        // Act
        var exception = new ConnectionException("Security error", authException, "SSL Failed", config);

        // Assert
        exception.Category.Should().Be(TcpClientExceptionCategory.Connection);
        exception.Severity.Should().Be(TcpClientExceptionSeverity.Error);
        exception.IsRetryable.Should().BeTrue();
        exception.InnerException.Should().Be(authException);
    }

    [Fact]
    public void ConnectionTimeoutException_ShouldProvideTimeoutDetails()
    {
        // Arrange
        var timeout = TimeSpan.FromSeconds(30);
        var config = new TcpClientConfig { Host = "slow.example.com", ConnectionTimeoutSeconds = 30 };

        // Act
        var exception = new ConnectionTimeoutException(timeout, "Connection timed out", null, "Timeout", config);

        // Assert
        exception.Category.Should().Be(TcpClientExceptionCategory.Timeout);
        exception.Severity.Should().Be(TcpClientExceptionSeverity.Warning);
        exception.IsRetryable.Should().BeTrue();
        exception.Timeout.Should().Be(timeout);
    }

    [Theory]
    [InlineData(SocketError.ConnectionRefused, true)]
    [InlineData(SocketError.TimedOut, true)]
    [InlineData(SocketError.NetworkUnreachable, true)]
    [InlineData(SocketError.AddressNotAvailable, false)]
    [InlineData(SocketError.InvalidArgument, false)]
    public void NetworkException_ShouldDetermineRetryabilityBasedOnSocketError(SocketError socketError, bool expectedRetryable)
    {
        // Arrange
        var socketException = new SocketException((int)socketError);
        var config = new TcpClientConfig { Host = "test.com", Port = 8080 };

        // Act
        var exception = new ConnectionException("Network error", socketException, "Failed", config);

        // Assert
        exception.IsRetryable.Should().Be(expectedRetryable);
    }

    [Fact]
    public void ExceptionChaining_ShouldPreserveOriginalStackTrace()
    {
        // Arrange
        Exception originalException;
        try
        {
            throw new InvalidOperationException("Original error");
        }
        catch (Exception ex)
        {
            originalException = ex;
        }

        // Act
        var wrappedException = new ConnectionException("Wrapped error", originalException, "Failed", new TcpClientConfig());

        // Assert
        wrappedException.InnerException.Should().Be(originalException);
        wrappedException.ToString().Should().Contain("Original error");
    }

    [Fact]
    public void ExceptionSerialization_ShouldPreserveCustomProperties()
    {
        // Arrange
        var config = new TcpClientConfig { Host = "test.com", Port = 8080 };
        var context = new Dictionary<string, object> { ["TestKey"] = "TestValue" };
        var originalException = new ConnectionException("Test error", null, "Failed", config, context);

        // Act & Assert - 验证异常包含所有必要信息
        originalException.Message.Should().Be("Test error");
        originalException.OperationContext.Should().ContainKey("TestKey");
        originalException.OperationContext.Should().ContainKey("TestKey");
        originalException.ConfigurationInfo.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public void ExceptionHandling_ShouldSanitizeSensitiveData()
    {
        // Arrange
        var config = new TcpClientConfig
        {
            Host = "secret-database.internal.com",
            Port = 5432,
            ClientId = "admin-user-12345",
            ClientName = "Production Database Client"
        };

        // Act
        var exception = new ConnectionException("Connection failed", null, "Failed", config);

        // Assert
        var configInfo = exception.ConfigurationInfo;
        configInfo.Should().NotContain("secret-database.internal.com");
        configInfo.Should().NotContain("admin-user-12345");
        configInfo.Should().NotContain("Production Database Client");

        // 应该包含脱敏后的信息
        configInfo.Should().Contain("Host: ****");
        configInfo.Should().Contain("ClientId: ****");
        configInfo.Should().Contain("ClientName: ****");
    }
}
