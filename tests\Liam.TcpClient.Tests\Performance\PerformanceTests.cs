using FluentAssertions;
using Liam.TcpClient.Extensions;
using Liam.TcpClient.Interfaces;
using Liam.TcpClient.Models;
using Microsoft.Extensions.Logging;
using Moq;
using System.Diagnostics;
using Xunit;
using Xunit.Abstractions;

namespace Liam.TcpClient.Tests.Performance;

/// <summary>
/// 性能压力测试
/// </summary>
public class PerformanceTests
{
    private readonly ITestOutputHelper _output;

    public PerformanceTests(ITestOutputHelper output)
    {
        _output = output;
    }

    [Fact]
    public async Task SendBatchAsync_ShouldHandleLargeBatch_WithinReasonableTime()
    {
        // Arrange
        var mockClient = new Mock<ITcpClient>();
        mockClient.Setup(c => c.SendMessageAsync(It.IsAny<TcpMessage>(), It.IsAny<CancellationToken>()))
                  .Returns((TcpMessage msg, CancellationToken ct) =>
                  {
                      return Task.Delay(1, ct).ContinueWith(_ => true, ct); // 模拟网络延迟
                  });

        var messageCount = 1000;
        var messages = Enumerable.Range(1, messageCount)
            .Select(i => TcpMessage.CreateTextMessage($"Message {i}"))
            .ToArray();

        var stopwatch = Stopwatch.StartNew();

        // Act
        var result = await mockClient.Object.SendBatchAsync(messages, maxConcurrency: 10);

        // Assert
        stopwatch.Stop();
        result.Should().Be(messageCount);
        
        // 应该在合理时间内完成（考虑并发，应该远少于顺序执行的时间）
        var maxExpectedTime = TimeSpan.FromSeconds(messageCount / 5); // 允许的最大时间
        stopwatch.Elapsed.Should().BeLessThan(maxExpectedTime);
        
        _output.WriteLine($"发送 {messageCount} 条消息耗时: {stopwatch.ElapsedMilliseconds}ms");
        _output.WriteLine($"平均每条消息: {(double)stopwatch.ElapsedMilliseconds / messageCount:F2}ms");
    }

    [Fact]
    public void TcpMessage_Serialize_ShouldBeEfficient_ForLargeData()
    {
        // Arrange
        var largeData = new byte[1024 * 1024]; // 1MB数据
        Random.Shared.NextBytes(largeData);
        
        var message = TcpMessage.CreateDataMessage(largeData);
        var stopwatch = Stopwatch.StartNew();

        // Act
        var serializedData = message.Serialize();

        // Assert
        stopwatch.Stop();
        serializedData.Should().NotBeNull();
        serializedData.Length.Should().Be(largeData.Length + 5); // 数据 + 头部
        
        // 序列化应该很快（< 100ms for 1MB）
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(100);
        
        _output.WriteLine($"序列化 1MB 数据耗时: {stopwatch.ElapsedMilliseconds}ms");
    }

    [Fact]
    public void TcpMessage_Deserialize_ShouldBeEfficient_ForLargeData()
    {
        // Arrange
        var largeData = new byte[1024 * 1024]; // 1MB数据
        Random.Shared.NextBytes(largeData);
        
        var originalMessage = TcpMessage.CreateDataMessage(largeData);
        var serializedData = originalMessage.Serialize();
        
        var stopwatch = Stopwatch.StartNew();

        // Act
        var deserializedMessage = TcpMessage.Deserialize(serializedData);

        // Assert
        stopwatch.Stop();
        deserializedMessage.Should().NotBeNull();
        deserializedMessage.Data.Should().BeEquivalentTo(largeData);
        
        // 反序列化应该很快（< 100ms for 1MB）
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(100);
        
        _output.WriteLine($"反序列化 1MB 数据耗时: {stopwatch.ElapsedMilliseconds}ms");
    }

    [Fact]
    public async Task ConnectionPool_ShouldHandleConcurrentRequests_Efficiently()
    {
        // Arrange
        var mockFactory = new Mock<ITcpClientFactory>();
        var mockLogger = new Mock<ILogger<TcpClientPool>>();
        
        var config = new TcpClientConfig
        {
            Host = "localhost",
            Port = 8080,
            ConnectionPoolConfig = new ConnectionPoolConfig
            {
                Enabled = true,
                PoolSize = 10
            }
        };

        var clientCreationCount = 0;
        mockFactory.Setup(f => f.CreateClient(config))
                  .Returns(() =>
                  {
                      Interlocked.Increment(ref clientCreationCount);
                      var mockClient = new Mock<ITcpClient>();
                      mockClient.Setup(c => c.Configuration).Returns(config);
                      mockClient.Setup(c => c.IsConnected).Returns(true);
                      return mockClient.Object;
                  });

        using var pool = new TcpClientPool(mockFactory.Object, config, mockLogger.Object);
        
        var concurrentRequests = 50;
        var stopwatch = Stopwatch.StartNew();

        // Act
        var tasks = Enumerable.Range(1, concurrentRequests)
            .Select(async i =>
            {
                var client = await pool.GetClientAsync();
                await Task.Delay(10); // 模拟使用客户端
                await pool.ReturnClientAsync(client);
                return client;
            });

        var results = await Task.WhenAll(tasks);

        // Assert
        stopwatch.Stop();
        results.Should().HaveCount(concurrentRequests);
        results.Should().OnlyContain(c => c != null);
        
        // 应该重用连接，创建的客户端数量应该远少于请求数量
        clientCreationCount.Should().BeLessThanOrEqualTo(config.ConnectionPoolConfig!.PoolSize);
        
        // 并发操作应该在合理时间内完成
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000);
        
        _output.WriteLine($"处理 {concurrentRequests} 个并发请求耗时: {stopwatch.ElapsedMilliseconds}ms");
        _output.WriteLine($"创建的客户端数量: {clientCreationCount}");
    }

    [Fact]
    public void TcpMessage_CreateAndDispose_ShouldNotLeakMemory()
    {
        // Arrange
        var initialMemory = GC.GetTotalMemory(true);
        var messageCount = 10000;

        // Act
        for (int i = 0; i < messageCount; i++)
        {
            var message = TcpMessage.CreateTextMessage($"Test message {i}");
            var serialized = message.Serialize();
            var deserialized = TcpMessage.Deserialize(serialized);
            
            // 确保对象被使用
            _ = deserialized.GetText();
        }

        // 强制垃圾回收
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();

        var finalMemory = GC.GetTotalMemory(true);

        // Assert
        var memoryIncrease = finalMemory - initialMemory;
        
        // 内存增长应该在合理范围内（< 10MB for 10k messages）
        memoryIncrease.Should().BeLessThan(10 * 1024 * 1024);
        
        _output.WriteLine($"初始内存: {initialMemory / 1024 / 1024:F2} MB");
        _output.WriteLine($"最终内存: {finalMemory / 1024 / 1024:F2} MB");
        _output.WriteLine($"内存增长: {memoryIncrease / 1024 / 1024:F2} MB");
    }

    [Fact]
    public async Task BatchOperations_ShouldScaleLinearlyWithConcurrency()
    {
        // Arrange
        var mockClient = new Mock<ITcpClient>();
        mockClient.Setup(c => c.SendMessageAsync(It.IsAny<TcpMessage>(), It.IsAny<CancellationToken>()))
                  .Returns((TcpMessage msg, CancellationToken ct) =>
                  {
                      return Task.Delay(10, ct).ContinueWith(_ => true, ct); // 固定延迟
                  });

        var messageCount = 100;
        var messages = Enumerable.Range(1, messageCount)
            .Select(i => TcpMessage.CreateTextMessage($"Message {i}"))
            .ToArray();

        // 测试不同并发级别
        var concurrencyLevels = new[] { 1, 5, 10, 20 };
        var results = new List<(int Concurrency, long ElapsedMs)>();

        // Act & Assert
        foreach (var concurrency in concurrencyLevels)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = await mockClient.Object.SendBatchAsync(messages, maxConcurrency: concurrency);
            stopwatch.Stop();

            result.Should().Be(messageCount);
            results.Add((concurrency, stopwatch.ElapsedMilliseconds));
            
            _output.WriteLine($"并发度 {concurrency}: {stopwatch.ElapsedMilliseconds}ms");
        }

        // 验证性能随并发度提升而改善
        for (int i = 1; i < results.Count; i++)
        {
            var current = results[i];
            var previous = results[i - 1];
            
            // 更高的并发度应该有更好的性能（更短的时间）
            current.ElapsedMs.Should().BeLessThanOrEqualTo(previous.ElapsedMs);
        }
    }
}
